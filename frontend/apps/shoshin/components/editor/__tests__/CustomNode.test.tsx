import { render, screen } from '@testing-library/react'
import { Position } from '@xyflow/react'
import { CustomNode } from '../CustomNode'

// Mock the @xyflow/react module
jest.mock('@xyflow/react', () => ({
  Handle: ({ type, position, id, className }: any) => (
    <div 
      data-testid={`handle-${type}-${position}-${id}`}
      className={className}
      data-type={type}
      data-position={position}
      data-id={id}
    />
  ),
  Position: {
    Top: 'top',
    Right: 'right',
    Bottom: 'bottom',
    Left: 'left',
  },
}))

describe('CustomNode', () => {
  const mockData = {
    label: 'Test Node',
    type: 'agent',
    description: 'Test description',
  }

  const defaultProps = {
    data: mockData,
    selected: false,
    id: 'test-node',
    position: { x: 0, y: 0 },
    type: 'custom',
  }

  it('should render node with correct label and description', () => {
    render(<CustomNode {...defaultProps} />)
    
    expect(screen.getByText('Test Node')).toBeInTheDocument()
    expect(screen.getByText('Test description')).toBeInTheDocument()
  })

  it('should render two handles with correct types and positions', () => {
    render(<CustomNode {...defaultProps} />)

    // Input handle (target type) - should be square shaped, positioned on top
    const inputTopHandle = screen.getByTestId('handle-target-top-input-top')

    // Output handle (source type) - should be circle shaped, positioned on bottom
    const outputBottomHandle = screen.getByTestId('handle-source-bottom-output-bottom')

    expect(inputTopHandle).toBeInTheDocument()
    expect(outputBottomHandle).toBeInTheDocument()
  })

  it('should apply square styling to input handle', () => {
    render(<CustomNode {...defaultProps} />)

    const inputTopHandle = screen.getByTestId('handle-target-top-input-top')

    // Check that input handle has !rounded-none class (square) and is 3x larger
    expect(inputTopHandle).toHaveClass('!rounded-none')
    expect(inputTopHandle).toHaveClass('w-12')
    expect(inputTopHandle).toHaveClass('h-12')
  })

  it('should apply circle styling to output handle', () => {
    render(<CustomNode {...defaultProps} />)

    const outputBottomHandle = screen.getByTestId('handle-source-bottom-output-bottom')

    // Check that output handle has !rounded-full class (circle) and is 3x larger
    expect(outputBottomHandle).toHaveClass('!rounded-full')
    expect(outputBottomHandle).toHaveClass('w-12')
    expect(outputBottomHandle).toHaveClass('h-12')
  })

  it('should apply selected styling when node is selected', () => {
    render(<CustomNode {...defaultProps} selected={true} />)
    
    const nodeContainer = screen.getByText('Test Node').closest('div')
    expect(nodeContainer).toHaveClass('border-primary-500')
    expect(nodeContainer).toHaveClass('shadow-lg')
  })

  it('should render correct icon based on node type', () => {
    render(<CustomNode {...defaultProps} />)
    
    // The icon should be rendered within the colored container
    const iconContainer = screen.getByText('Test Node').closest('div')?.querySelector('.bg-primary-500')
    expect(iconContainer).toBeInTheDocument()
  })

  it('should handle node without description', () => {
    const dataWithoutDescription = {
      label: 'Test Node',
      type: 'agent',
    }
    
    render(<CustomNode {...defaultProps} data={dataWithoutDescription} />)
    
    expect(screen.getByText('Test Node')).toBeInTheDocument()
    expect(screen.queryByText('Test description')).not.toBeInTheDocument()
  })

  it('should use default icon and color for unknown node type', () => {
    const dataWithUnknownType = {
      label: 'Unknown Node',
      type: 'unknown-type',
    }
    
    render(<CustomNode {...defaultProps} data={dataWithUnknownType} />)
    
    expect(screen.getByText('Unknown Node')).toBeInTheDocument()
    // Should fall back to default styling
  })
})
